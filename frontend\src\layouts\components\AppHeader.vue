<template>
  <el-header>
    <div class="w-full h-full lg:px-4 lg:pl-2">
      <div class="grid grid-cols-3 items-center h-full">
        <div class="flex items-center pl-1">
          <router-link to="/" class="no-drag flex items-center logo-container">
            <img src="@/assets/images/logo256.svg" alt="云雀 Logo" class="logo-image" />
            <span class="logo-text">云雀</span>
          </router-link>
        </div>
        <div class="flex items-center justify-center">
          <el-input v-model="searchData" placeholder="智能搜索" class="no-drag search-input" @keydown.enter="openSearch">
            <template #prefix>
              <font-awesome-icon :icon="['fas', 'search']" class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>
          <SearchComp ref="searchDialog" :data="searchData" />
          <CreateBtn ref="createDialog" class="no-drag">
            <template #default="{ open }">
              <button
                @click="open"
                class="create-btn flex h-8 w-8 items-center justify-center rounded-lg ml-3 transition-all duration-200 hover:scale-105 active:scale-95"
              >
                <font-awesome-icon :icon="['fas', 'plus']" class="w-4 h-4" />
              </button>
            </template>
          </CreateBtn>
        </div>
        <div class="flex items-center justify-end space-x-2">
          <BrowserBar class="no-drag" />
          <NonBrowser class="no-drag" v-if="isEE" />
        </div>
      </div>
    </div>
  </el-header>
</template>

<script setup lang="ts" name="Header">
import { ref } from "vue";
import SearchComp from "@/components/SearchComp/index.vue";
import CreateBtn from "./CreateBtn.vue";
import BrowserBar from "./BrowserBar.vue";
import NonBrowser from "./NonBrowser.vue";
import { ElMessage } from "element-plus";
import { isEE } from "@/utils/ipcRenderer";

const searchData = ref("");
const searchDialog: any = ref(null);

const openSearch = () => {
  if (!searchData.value) {
    ElMessage.error("请输入有效的搜索内容！");
    return;
  }
  if (searchDialog.value) searchDialog.value.openDialog();
  searchData.value = "";
};
</script>

<style scoped lang="scss">
.el-header {
  -webkit-app-region: drag;
  @apply fixed z-30 border-b border-gray-200 bg-white;
  height: 48px;
  padding: 0;
  border-radius: 0;
  left: 0;
  right: 0;

  .logo-container {
    @apply transition-all duration-200 hover:opacity-80;

    .logo-image {
      @apply w-11 h-11 mr-1 transition-all duration-200;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    .logo-text {
      @apply self-center whitespace-nowrap text-2xl font-bold tracking-normal;
      color: #374151;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
        "Helvetica Neue", Helvetica, Arial, sans-serif;
    }

    &:hover {
      .logo-image {
        @apply scale-105;
      }
      .logo-text {
        color: #1f2937;
      }
    }
  }
  .el-input.search-input {
    @apply h-8 w-64 rounded-lg border border-gray-200/60 bg-gray-50/90 py-0 transition-all hover:bg-gray-50 hover:border-gray-300/80 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.7);
  }

  .create-btn {
    @apply cursor-pointer;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);

    &:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.15) 100%);
      border-color: rgba(59, 130, 246, 0.3);
      color: #2563eb;
      box-shadow:
        0 2px 6px rgba(59, 130, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    &:active {
      box-shadow: inset 0 2px 4px rgba(59, 130, 246, 0.1);
    }
  }
  :deep(.search-input) {
    .el-input__wrapper {
      box-shadow: none;
      @apply bg-transparent;
      .el-input__inner,
      .el-input__inner::placeholder {
        @apply text-sm;
      }
    }
  }
}

.dark {
  .el-header {
    @apply bg-gray-800 border-gray-700;

    .logo-container {
      .logo-text {
        color: #d1d5db;
      }

      &:hover {
        .logo-text {
          color: #f3f4f6;
        }
      }
    }
    .el-input {
      @apply bg-gray-700/60 border-gray-600/60 text-gray-300 hover:bg-gray-600/70 hover:border-gray-500/70 focus:ring-blue-400/50 focus:border-blue-400/50;
      box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .create-btn {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.15) 100%);
      border-color: rgba(59, 130, 246, 0.3);
      color: #60a5fa;
      box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);

      &:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
        border-color: rgba(59, 130, 246, 0.4);
        color: #93c5fd;
        box-shadow:
          0 2px 6px rgba(59, 130, 246, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.15);
      }
    }
    

  }
}

.no-drag {
  -webkit-app-region: no-drag;
}


</style>
