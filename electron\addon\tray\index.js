const { Tray, <PERSON>u, ipc<PERSON>ain, app } = require("electron");
const path = require("path");
const Ps = require("ee-core/ps");
const Log = require("ee-core/log");
const Electron = require("ee-core/electron");
const CoreWindow = require("ee-core/electron/window");
const Conf = require("ee-core/config");
const EE = require("ee-core/ee");
const Services = require("ee-core/services");

/**
 * 托盘插件
 * @class
 */
class TrayAddon {
  constructor() {
    this.tray = null;
    this.blinkInterval = null; // 新增变量，控制闪烁间隔
    this.iconPathNormal = ""; // 正常图标路径
    this.iconPathBlink = ""; // 闪烁时使用的图标路径
    this.isBlinking = false; // 是否正在闪烁
  }

  /**
   * 创建托盘
   */
  create() {
    // 开发环境，代码热更新开启时，会导致托盘中有残影
    if (Ps.isDev() && Ps.isHotReload()) return;

    Log.info("[addon:tray] load");
    const { CoreApp } = EE;
    const cfg = Conf.getValue("addons.tray");
    const mainWindow = CoreWindow.getMainWindow();

    // 根据操作系统和DPI选择合适的托盘图标格式
    const os = require('os');
    const { screen } = require('electron');
    const platform = os.platform();
    let trayIconName, trayIconEmptyName;
    
    // 获取主显示器的缩放因子
    const scaleFactor = screen.getPrimaryDisplay().scaleFactor || 1;
    Log.info(`[addon:tray] 显示器缩放因子: ${scaleFactor}`);
    
    if (platform === 'win32') {
      // Windows 优先使用 ICO 格式，包含多种尺寸
      trayIconName = "/public/images/tray.ico";
      trayIconEmptyName = scaleFactor >= 2 ? "/public/images/tray_32.png" : "/public/images/tray_16.png";
    } else if (platform === 'darwin') {
      // macOS 使用 PNG 格式，系统会自动处理模板模式和缩放
      trayIconName = "/public/images/tray_16.png";
      trayIconEmptyName = "/public/images/tray_empty.png";
    } else {
      // Linux 根据缩放因子选择合适尺寸
      trayIconName = scaleFactor >= 2 ? "/public/images/tray_32.png" : "/public/images/tray_16.png";
      trayIconEmptyName = "/public/images/tray_empty.png";
    }

    // 托盘图标
    this.iconPathNormal = path.join(Ps.getHomeDir(), trayIconName);
    this.iconPathBlink = path.join(Ps.getHomeDir(), trayIconEmptyName);

    // 检查图标文件是否存在
    const fs = require('fs');
    if (!fs.existsSync(this.iconPathNormal)) {
      Log.error(`[addon:tray] 托盘图标文件不存在: ${this.iconPathNormal}`);
      // 使用默认图标作为后备
      this.iconPathNormal = path.join(Ps.getHomeDir(), "build/icons/16x16.png");
    }
    if (!fs.existsSync(this.iconPathBlink)) {
      Log.error(`[addon:tray] 托盘闪烁图标文件不存在: ${this.iconPathBlink}`);
      // 使用默认图标作为后备
      this.iconPathBlink = path.join(Ps.getHomeDir(), "build/icons/16x16.png");
    }

    Log.info(`[addon:tray] 平台: ${platform}`);
    Log.info(`[addon:tray] 使用托盘图标: ${this.iconPathNormal}`);
    Log.info(`[addon:tray] 使用闪烁图标: ${this.iconPathBlink}`);

    // 托盘菜单功能列表
    let trayMenuTemplate = [
      {
        label: "显示主窗口",
        click: function () {
          mainWindow.show();
          mainWindow.focus();
        },
      },
      {
        type: 'separator'
      },
      {
        label: "设置",
        click: () => this.showConfigWindow(),
      },
      {
        type: 'separator'
      },
      {
        label: "重启应用",
        click: function () {
          app.relaunch();
          app.quit();
        },
      },
      {
        label: "退出",
        click: function () {
          CoreApp.appQuit();
        },
      },
    ];
    // 点击关闭，最小化到托盘
    mainWindow.on("close", (event) => {
      if (Electron.extra.closeWindow == true) {
        return;
      }
      mainWindow.hide();
      event.preventDefault();
    });

    // 实例化托盘
    try {
      this.tray = new Tray(this.iconPathNormal);
      
      // macOS 设置为模板图标以获得最佳显示效果
      if (platform === 'darwin') {
        this.tray.setTemplateImage(this.iconPathNormal);
      }
      
      this.tray.setToolTip(cfg.title);
      const contextMenu = Menu.buildFromTemplate(trayMenuTemplate);
      this.tray.setContextMenu(contextMenu);
      
      // 托盘图标点击事件
      this.tray.on("click", () => {
        if (mainWindow.isVisible()) {
          mainWindow.hide();
        } else {
          mainWindow.show();
          mainWindow.focus();
        }
      });
      
      this.tray.on("double-click", () => {
        mainWindow.show();
        mainWindow.focus();
      });
      
      Log.info("[addon:tray] 托盘创建成功");
    } catch (error) {
      Log.error(`[addon:tray] 托盘创建失败: ${error.message}`);
      return;
    }

    // 设置托盘闪烁监听
    ipcMain.on("message-start-blinking", (event, data) => {
      this.startBlinking();
    });
    ipcMain.on("message-stop-blinking", (event) => {
      this.stopBlinking();
    });
  }

  startBlinking() {
    if (this.blinkInterval || !this.tray) return; // 已经在闪烁中或托盘不存在

    const step = 450; // 闪烁间隔毫秒
    this.isBlinking = true;
    
    try {
      const toggleTrayImg = () => {
        if (this.tray && !this.tray.isDestroyed()) {
          this.tray.setImage(
            this.isBlinking ? this.iconPathBlink : this.iconPathNormal
          );
          this.isBlinking = !this.isBlinking;
        }
      };
      
      toggleTrayImg();
      this.blinkInterval = setInterval(() => {
        toggleTrayImg();
      }, step);
      
      Log.info("[addon:tray] 开始闪烁");
    } catch (error) {
      Log.error(`[addon:tray] 闪烁启动失败: ${error.message}`);
      this.stopBlinking();
    }
  }

  stopBlinking() {
    if (this.blinkInterval) {
      this.isBlinking = false;
      clearInterval(this.blinkInterval);
      this.blinkInterval = null;
      
      // 停止闪烁后，重置为原始图标
      try {
        if (this.tray && !this.tray.isDestroyed()) {
          this.tray.setImage(this.iconPathNormal);
        }
        Log.info("[addon:tray] 停止闪烁");
      } catch (error) {
        Log.error(`[addon:tray] 停止闪烁时出错: ${error.message}`);
      }
    }
  }

  // 显示配置窗口
  showConfigWindow() {
    Services.get("os").showConfigWindow("#/config");
  }

  // 销毁托盘
  destroy() {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
    this.stopBlinking();
    Log.info("[addon:tray] 托盘已销毁");
  }

  // 确保托盘图标文件存在
  ensureTrayIcons() {
    const fs = require('fs');
    const path = require('path');
    
    // 如果托盘图标不存在，复制默认图标
    if (!fs.existsSync(this.iconPathNormal)) {
      const defaultIcon = path.join(Ps.getHomeDir(), "build/icons/16x16.png");
      if (fs.existsSync(defaultIcon)) {
        try {
          fs.copyFileSync(defaultIcon, this.iconPathNormal);
          Log.info(`[addon:tray] 已创建托盘图标: ${this.iconPathNormal}`);
        } catch (error) {
          Log.error(`[addon:tray] 创建托盘图标失败: ${error.message}`);
        }
      }
    }
  }
}

TrayAddon.toString = () => "[class TrayAddon]";
module.exports = TrayAddon;
