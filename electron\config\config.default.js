"use strict";

const path = require("path");

// 加载外部配置文件
const appjson = require("../database/appjson");
const outerCofig = appjson.getSystemConfig() || {};

/**
 * 默认配置
 */
module.exports = (appInfo) => {
  const config = {};

  /**
   * 开发者工具
   */
  config.openDevTools = outerCofig.openDevTools || false;

  /**
   * 应用程序顶部菜单
   */
  config.openAppMenu = false;

  /**
   * 主窗口
   */
  config.windowsOption = {
    title: "云雀",
    ...(outerCofig.windowSize
      ? outerCofig.windowSize
      : {
          width: 1250,
          height: 800,
          minWidth: 1250,
          minHeight: 800,
        }),
    webPreferences: {
      //webSecurity: false,
      contextIsolation: false, // false -> 可在渲染进程中使用electron的api，true->需要bridge.js(contextBridge)
      nodeIntegration: true,
      //preload: path.join(appInfo.baseDir, 'preload', 'bridge.js'),
    },
    transparent: false, // 设置窗口不透明
    frame: false,
    shadow: false, // 去掉窗口阴影
    show: true,
    maximizable: true, // 允许最大化
    minimizable: true, // 允许最小化
    resizable: true,   // 允许调整大小
    fullscreenable: true, // 允许全屏
    icon: path.join(appInfo.home, "build", "icons", "icon.png"),
  };

  /**
   * 框架日志
   */
  config.logger = {
    encoding: "utf8",
    level: "INFO",
    outputJSON: false,
    buffer: true,
    enablePerformanceTimer: false,
    rotator: "day",
    appLogName: "lark.log",
    coreLogName: "lark-core.log",
    errorLogName: "lark-error.log",
  };

  /**
   * 远程模式-web地址
   *   enable: false, // 是否启用,不启用的话打开的是本地前端
   */
  config.remoteUrl = outerCofig.remoteUrl
    ? outerCofig.remoteUrl
    : {
        enable: false,
        url: "http://*************",
      };

  /**
   * 内置socket服务
   */
  config.socketServer = {
    enable: false,
    port: 7070,
    path: "/socket.io/",
    connectTimeout: 45000,
    pingTimeout: 30000,
    pingInterval: 25000,
    maxHttpBufferSize: 1e8,
    transports: ["polling", "websocket"],
    cors: {
      origin: true,
    },
    channel: "c1",
  };

  /**
   * 内置http服务
   */
  config.httpServer = {
    enable: false,
    https: {
      enable: false,
      key: "/public/ssl/localhost+1.key",
      cert: "/public/ssl/localhost+1.pem",
    },
    host: "127.0.0.1",
    port: 7071,
    cors: {
      origin: "*",
    },
    body: {
      multipart: true,
      formidable: {
        keepExtensions: true,
      },
    },
    filterRequest: {
      uris: ["favicon.ico"],
      returnData: "",
    },
  };

  /**
  * protocol string - 支持：http://、 https://、file:// 三种协议，框架会创建不同服务。
  * indexPath string - 前端资源入口文件
  * host string - web模式服务地址（127.0.0.1 或 localhost）
  * port integer - web模式端口
  * ssl object - 使用https协议时的证书文件。
  * options object - 一些额外的参数。
  }; 
   */
  config.mainServer = {
    protocol: "file://",
    indexPath: "/public/dist/index.html",
  };

  /**
   * 硬件加速
   */
  config.hardGpu = {
    enable: false,
  };

  /**
   * 异常捕获
   */
  config.exception = {
    mainExit: false,
    childExit: true,
    rendererExit: true,
  };

  /**
   * jobs
   */
  config.jobs = {
    messageLog: true,
  };

  /**
   * 插件功能
   */
  config.addons = {
    window: {
      enable: true,
    },
    tray: {
      enable: true,
      title: "云雀",
      icon: "/public/images/tray_16.png",
      icon_empty: "/public/images/tray_empty.png",
    },
    notice: {
      console_notice: false,
      group_icon: "/public/images/notice/group_icon.png",
      system_icon: "/public/images/notice/system_icon.png",
      user_icon: "/public/images/notice/user_icon.png",
    },
    security: {
      enable: true,
    },
    awaken: {
      enable: true,
      protocol: "ee",
      args: [],
    },
    autoUpdater: {
      enable: true,
      windows: false,
      macOS: false,
      linux: false,
      options: {
        provider: "generic",
        url: "http://kodo.qiniu.com/",
      },
      force: false,
    },
    floatingball: {
      enable: true,
      isAutoStartup: false,
      isOpenAtStartup: true,
      isShowTrayIcon: true,
      isBallShow: true,
      isBallAlwaysOnTop: true,
    },
  };

  /**
   * 悬浮球
   */
  config.floatingBall = {
    isAutoStartup: false,
    isOpenAtStartup: true,
    isShowTrayIcon: true,
    isBallShow: true,
    isBallAlwaysOnTop: true,
  };
  // // 开发模式前端服务（渲染进程）
  // config.developmentMode= {
  //   default: 'vue', // 默认前后端分离，使用vue
  //   mode: {
  //   	// 前后端分离，使用vue开发，端口与vue启动的serve一致
  //     vue: {
  //       hostname: 'localhost',
  //       port: 8080
  //     }
  //   }
  // };

  return {
    ...config,
  };
};
